import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import LoadingSpinner from '@/components/ui/loading-spinner';
import AuthErrorDisplay from '@/components/AuthErrorDisplay';
import AuthErrorBoundary from '@/components/AuthErrorBoundary';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireProfile?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireProfile = false
}) => {
  const { user, profile, loading, error } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner text="Checking authentication..." />
      </div>
    );
  }

  // Redirect to auth page if not authenticated
  if (!user) {
    return (
      <Navigate
        to="/auth"
        state={{ from: location }}
        replace
      />
    );
  }

  // Special handling for profile-setup route - prevent redirect loops
  if (location.pathname === '/profile-setup') {
    // If user already has a profile and tries to access profile-setup, redirect to dashboard
    if (profile) {
      return (
        <Navigate
          to="/dashboard"
          replace
        />
      );
    }
    // Allow access to profile-setup if no profile exists
    return <>{children}</>;
  }

  // If profile is required but doesn't exist, redirect to profile setup
  if (requireProfile && !profile) {
    return (
      <Navigate
        to="/profile-setup"
        state={{ from: location }}
        replace
      />
    );
  }

  // User is authenticated and meets requirements, render the protected content
  return (
    <AuthErrorBoundary>
      <AuthErrorDisplay />
      {children}
    </AuthErrorBoundary>
  );
};

export default ProtectedRoute;
