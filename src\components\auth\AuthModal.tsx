import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Heart } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import {
  handleSupabaseError,
  isAuthError,
  isValidationError,
} from "@/types/app";
import { SignInForm } from "@/components/auth/SignInForm";
import { SimpleSignUpForm } from "@/components/auth/SimpleSignUpForm";
import { SignInFormData, SignUpFormData } from "@/components/auth/schemas";

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultMode?: 'signin' | 'signup';
}

export function AuthModal({ isOpen, onClose, defaultMode = 'signin' }: AuthModalProps) {
  const [isSignUp, setIsSignUp] = useState(defaultMode === 'signup');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const onSignIn = async (values: SignInFormData) => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password,
      });

      if (error) throw error;

      toast({
        title: "Success!",
        description: "You have been signed in successfully.",
      });
      
      onClose(); // Close modal
      navigate('/dashboard');
    } catch (error: unknown) {
      if (isAuthError(error)) {
        toast({
          title: "Authentication Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        handleSupabaseError(error);
      }
    } finally {
      setLoading(false);
    }
  };

  const onSignUp = async (values: SignUpFormData) => {
    setLoading(true);
    try {
      console.log('Starting signup process...');

      // Create the user account only
      const { error: signUpError, data } = await supabase.auth.signUp({
        email: values.email,
        password: values.password,
      });

      if (signUpError) {
        console.error('Signup error:', signUpError);
        throw signUpError;
      }

      if (!data.user) {
        console.error('No user data returned from signup');
        throw new Error('No user data returned');
      }

      console.log('User created successfully:', data.user.id);

      // Wait for session to be established
      console.log('Waiting for session to be established...');
      let sessionEstablished = false;
      let attempts = 0;
      const maxAttempts = 10;

      while (!sessionEstablished && attempts < maxAttempts) {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user?.id === data.user.id) {
          sessionEstablished = true;
          console.log('Session established successfully');
        } else {
          attempts++;
          await new Promise(resolve => setTimeout(resolve, 500)); // Wait 500ms
        }
      }

      if (!sessionEstablished) {
        console.warn('Session not established after signup, proceeding anyway');
      }

      toast({
        title: "Account Created!",
        description: "Please complete your profile setup to continue.",
      });

      onClose(); // Close modal
      // Redirect to dashboard - ProtectedRoute will redirect to profile setup
      navigate('/dashboard');
    } catch (error: unknown) {
      console.error('Signup error:', error);
      if (isAuthError(error)) {
        toast({
          title: "Authentication Error",
          description: error.message,
          variant: "destructive",
        });
      } else if (isValidationError(error)) {
        toast({
          title: "Validation Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        handleSupabaseError(error);
      }
    } finally {
      setLoading(false);
    }
  };

  // Reset to default mode when modal opens
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    } else {
      setIsSignUp(defaultMode === 'signup');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Heart className="h-12 w-12 text-pink-500" />
          </div>
          <DialogTitle className="text-2xl">
            {isSignUp ? "Create Your Wedding Account" : "Welcome Back"}
          </DialogTitle>
          <DialogDescription>
            {isSignUp 
              ? "Set up your wedding pledge management account" 
              : "Sign in to manage your wedding pledges"
            }
          </DialogDescription>
        </DialogHeader>
        
        <div className="mt-6">
          {isSignUp ? (
            <SimpleSignUpForm onSubmit={onSignUp} loading={loading} />
          ) : (
            <SignInForm onSubmit={onSignIn} loading={loading} />
          )}
          
          <div className="mt-4 text-center">
            <Button
              variant="link"
              onClick={() => setIsSignUp(!isSignUp)}
              disabled={loading}
            >
              {isSignUp 
                ? "Already have an account? Sign in" 
                : "Don't have an account? Sign up"
              }
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
