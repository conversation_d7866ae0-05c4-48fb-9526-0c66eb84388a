import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { Tables } from '@/integrations/supabase/types';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: Tables<'profiles'> | null;
  loading: boolean;
  error: string | null;
  signOut: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<Tables<'profiles'> | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [profileFetching, setProfileFetching] = useState(false);

  const fetchProfile = async (userId: string): Promise<void> => {
    // Prevent multiple simultaneous profile fetches
    if (profileFetching) {
      console.log('Profile fetch already in progress, skipping');
      return;
    }

    try {
      setProfileFetching(true);
      console.log('Fetching profile for user:', userId);
      setError(null); // Clear any previous errors

      // Add timeout to profile fetch to prevent hanging
      const profileFetchPromise = supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .maybeSingle();

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Profile fetch timeout')), 5000);
      });

      const { data, error } = await Promise.race([profileFetchPromise, timeoutPromise]) as any;

      if (error) {
        console.error('Database error fetching profile:', error);
        // Distinguish between different types of database errors
        if (error.code === 'PGRST116') {
          // No rows returned - profile doesn't exist (not an error)
          console.log('No profile found for user:', userId);
          setProfile(null);
        } else {
          // Actual database error
          console.warn(`Failed to load profile: ${error.message}`);
          // Don't set error state for profile fetch failures during auth init
          setProfile(null);
        }
        return;
      }

      if (data) {
        console.log('Profile fetched successfully:', data);
        setProfile(data);
      } else {
        console.log('No profile found for user:', userId);
        setProfile(null);
      }
    } catch (error) {
      console.error('Unexpected error fetching profile:', error);
      if (error instanceof Error && error.message === 'Profile fetch timeout') {
        console.warn('Profile fetch timed out, continuing without profile');
      } else {
        console.warn('Profile fetch failed, continuing without profile');
      }
      setProfile(null);
    } finally {
      setProfileFetching(false);
    }
  };

  const refreshProfile = async () => {
    if (user) {
      await fetchProfile(user.id);
    }
  };

  const clearError = () => {
    setError(null);
  };

  const signOut = async () => {
    console.log('Signing out user, current state:', {
      user: user?.id,
      session: session?.user?.id,
      profile: profile?.id,
      profileFetching
    });
    setError(null); // Clear any errors on sign out

    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Sign out error:', error);
        setError(`Failed to sign out: ${error.message}`);
        throw error;
      }
      console.log('Sign out successful, clearing local state');

      // ✅ Clear local state immediately on successful logout
      setSession(null);
      setUser(null);
      setProfile(null);
      setProfileFetching(false); // Also reset profile fetching flag

      console.log('Local state cleared after sign out');
    } catch (error) {
      // Even if sign out fails, clear local state to prevent stuck state
      console.error('Sign out failed, clearing local state:', error);
      setSession(null);
      setUser(null);
      setProfile(null);
      setProfileFetching(false); // Also reset profile fetching flag
      throw error;
    }
  };

  useEffect(() => {
    let isMounted = true;
    let isInitialized = false;

    // Safety timeout to prevent infinite loading (reduced from 10s to 5s)
    const safetyTimeout = setTimeout(() => {
      if (isMounted) {
        console.warn('Auth initialization timeout reached after 5 seconds, setting loading to false');
        console.warn('Current state:', { user: user?.id, session: session?.user?.id, profile: profile?.id });
        setLoading(false);
      }
    }, 5000); // 5 seconds

    const handleAuthState = async (session: Session | null, skipProfileFetch = false) => {
      if (!isMounted) return;

      console.log('Handling auth state:', session?.user?.id, 'skipProfileFetch:', skipProfileFetch);

      setSession(session);
      setUser(session?.user ?? null);

      // Set loading to false immediately after setting user/session
      // Don't wait for profile fetch to complete
      if (isMounted) {
        setLoading(false);
        clearTimeout(safetyTimeout);
      }

      // Fetch profile asynchronously without blocking auth initialization
      if (session?.user && !skipProfileFetch) {
        // Don't await - let it run in background
        fetchProfile(session.user.id).catch(error => {
          console.error('Error fetching profile:', error);
        });
      } else if (!session?.user) {
        setProfile(null);
      }
    };

    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id, 'isInitialized:', isInitialized);

        // Skip profile fetch only for the initial session event (already handled by getSession)
        // But always handle SIGNED_OUT and other events
        if (event === 'INITIAL_SESSION' && !isInitialized) {
          console.log('Skipping INITIAL_SESSION event - already handled by getSession');
          return;
        }

        // Handle SIGNED_OUT event specifically
        if (event === 'SIGNED_OUT') {
          console.log('Processing SIGNED_OUT event');
          setSession(null);
          setUser(null);
          setProfile(null);
          setProfileFetching(false);
          return;
        }

        await handleAuthState(session);
      }
    );

    // Check for existing session on mount
    const initializeAuth = async () => {
      try {
        console.log('Starting auth initialization...');
        const { data: { session } } = await supabase.auth.getSession();
        console.log('Initial session check:', session?.user?.id);

        await handleAuthState(session);
        isInitialized = true;
        console.log('Auth initialization completed successfully');
      } catch (error) {
        console.error('Error initializing auth:', error);
        if (isMounted) {
          setLoading(false);
          clearTimeout(safetyTimeout);
        }
      }
    };

    initializeAuth();

    return () => {
      isMounted = false;
      clearTimeout(safetyTimeout);
      subscription.unsubscribe();
    };
  }, []);

  const value: AuthContextType = {
    user,
    session,
    profile,
    loading,
    error,
    signOut,
    refreshProfile,
    clearError,
  };



  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
