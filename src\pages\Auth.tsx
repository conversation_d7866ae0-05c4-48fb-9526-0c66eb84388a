import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { Heart } from "lucide-react";
import {
  handleSupabaseError,
  isAuthError,
  isValidationError,
  isDatabaseError,
} from "@/types/app";
import { SignInForm } from "@/components/auth/SignInForm";
import { SimpleSignUpForm } from "@/components/auth/SimpleSignUpForm";

import type { SignInFormData, SignUpFormData } from "@/components/auth/schemas";
import { useSEO } from "@/utils/seo";
import { BreadcrumbStructuredData } from "@/components/StructuredData";

const Auth = () => {
  const [isSignUp, setIsSignUp] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  // SEO optimization for auth page
  useSEO('auth');

  const onSignIn = async (values: SignInFormData) => {
    setLoading(true);
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password,
      });

      if (error) throw error;

      toast({
        title: "Success!",
        description: "You have been signed in successfully.",
      });
      navigate('/dashboard');
    } catch (error: unknown) {
      if (isAuthError(error)) {
        toast({
          title: "Authentication Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        handleSupabaseError(error);
      }
    } finally {
      setLoading(false);
    }
  };

  const onSignUp = async (values: SignUpFormData) => {
    setLoading(true);
    try {
      console.log('Starting signup process...');

      // Create the user account only
      const { error: signUpError, data } = await supabase.auth.signUp({
        email: values.email,
        password: values.password,
      });

      if (signUpError) {
        console.error('Signup error:', signUpError);
        throw signUpError;
      }

      if (!data.user) {
        console.error('No user data returned from signup');
        throw new Error('No user data returned');
      }

      console.log('User created successfully:', data.user.id);

      toast({
        title: "Account Created!",
        description: "Please complete your profile setup to continue.",
      });

      // Redirect to dashboard - ProtectedRoute will redirect to profile setup
      navigate('/dashboard');
    } catch (error: unknown) {
      console.error('Signup error:', error);
      if (isAuthError(error)) {
        toast({
          title: "Authentication Error",
          description: error.message,
          variant: "destructive",
        });
      } else if (isValidationError(error)) {
        toast({
          title: "Validation Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        handleSupabaseError(error);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 flex items-center justify-center p-4">
      {/* Breadcrumb Structured Data */}
      <BreadcrumbStructuredData
        items={[
          { name: "Home", url: "https://pledgeforlove.ug" },
          { name: "Sign In", url: "https://pledgeforlove.ug/auth" }
        ]}
      />
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Heart className="h-12 w-12 text-pink-500" />
          </div>
          <CardTitle className="text-2xl">
            {isSignUp ? "Create Your Wedding Account" : "Welcome Back"}
          </CardTitle>
          <CardDescription>
            {isSignUp 
              ? "Set up your wedding pledge management account" 
              : "Sign in to manage your wedding pledges"
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isSignUp ? (
            <SimpleSignUpForm onSubmit={onSignUp} loading={loading} />
          ) : (
            <SignInForm onSubmit={onSignIn} loading={loading} />
          )}
          
          <div className="mt-4 text-center">
            <Button
              variant="link"
              onClick={() => setIsSignUp(!isSignUp)}
            >
              {isSignUp 
                ? "Already have an account? Sign in" 
                : "Don't have an account? Sign up"
              }
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Auth;
